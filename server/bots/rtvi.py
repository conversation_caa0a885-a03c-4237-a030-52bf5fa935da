from bots.rtvi_actions import register_rtvi_actions
from bots.rtvi_services import register_rtvi_services
from bots.types import BotConfig
from pipecat.processors.aggregators.llm_response import LLMUserContextAggregator
from pipecat.processors.frameworks.rtvi import (
    RTVIConfig,
    RTVIProcessor,
    RTVIServiceConfig,
    RTVIServiceOptionConfig,
)


async def create_rtvi_processor(
    bot_config: BotConfig, user_aggregator: LLMUserContextAggregator
) -> RTVIProcessor:
    config = bot_config["config"] if isinstance(bot_config, dict) else bot_config.config

    #
    # RTVI default config
    #
    default_config = RTVIConfig(
        config=[
            RTVIServiceConfig(
                service="llm",
                options=[
                    RTVIServiceOptionConfig(
                        name="model", value="apac.amazon.nova-lite-v1:0"
                    )
                ],
            ),
            RTVIServiceConfig(
                service="tts",
                options=[
                    RTVIServiceOptionConfig(name="voice_id", value="Joanna"),
                    RTVIServiceOptionConfig(name="engine", value="neural"),
                    RTVIServiceOptionConfig(name="rate", value="1.0"),
                ],
            ),
            RTVIServiceConfig(
                service="stt",
                options=[
                    RTVIServiceOptionConfig(name="language_code", value="en-US"),
                ],
            ),
        ]
    )

    #
    # RTVI processor
    #

    config = RTVIConfig(config=config) if config else default_config

    rtvi = RTVIProcessor(config=config)

    await register_rtvi_services(rtvi, user_aggregator)
    await register_rtvi_actions(rtvi, user_aggregator)

    return rtvi
